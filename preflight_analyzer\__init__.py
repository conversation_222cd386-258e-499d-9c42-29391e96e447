"""
Preflight Analyzer with Enhanced Multi-Layer Cache Architecture.

This module provides the enhanced preflight analyzer with a robust
multi-layer cache system that dramatically reduces cache miss rates
through content-based keys and GUID reconciliation.
"""

from .history_store import DecisionHistory
from .enhanced_cache import EnhancedDecisionCache
from .multi_layer_cache import MultiLayerCache
from .cache_models import AnalysisResult, ContentKey
from .cache_observability import CacheMetrics, CacheLogger, CacheHealthMonitor

__version__ = "3.0.0"
__all__ = [
    'DecisionHistory',
    'EnhancedDecisionCache',
    'MultiLayerCache',
    'AnalysisResult',
    'ContentKey',
    'CacheMetrics',
    'CacheLogger',
    'CacheHealthMonitor'
]
