"""
Cache Migration Utilities.

This module provides utilities to migrate from the legacy DecisionHistory
JSON-based cache to the new multi-layer cache architecture.
"""

from __future__ import annotations
import json
import logging
import re
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

from .cache_models import <PERSON><PERSON><PERSON><PERSON>, <PERSON>Key, extract_quality_from_title
from .multi_layer_cache import MultiLayerCache

logger = logging.getLogger(__name__)


class CacheMigrator:
    """Handles migration from legacy cache formats to multi-layer cache."""
    
    def __init__(self, multi_layer_cache: MultiLayerCache):
        self.cache = multi_layer_cache
        self.migration_stats = {
            'processed': 0,
            'migrated': 0,
            'skipped': 0,
            'errors': 0
        }
    
    def migrate_decision_history(self, decision_history_path: Path) -> Dict[str, int]:
        """
        Migrate legacy decision_history.json to new cache format.
        
        Args:
            decision_history_path: Path to the legacy decision_history.json file
            
        Returns:
            Dictionary with migration statistics
        """
        if not decision_history_path.exists():
            logger.warning(f"Decision history file not found: {decision_history_path}")
            return self.migration_stats
        
        logger.info(f"Starting migration from {decision_history_path}")
        
        try:
            with open(decision_history_path, 'r', encoding='utf-8') as f:
                legacy_data = json.load(f)
        except Exception as e:
            logger.error(f"Failed to load legacy decision history: {e}")
            return self.migration_stats
        
        for guid_key, entry in legacy_data.items():
            self.migration_stats['processed'] += 1
            
            try:
                # Parse the legacy entry
                analysis_result = self._convert_legacy_entry(guid_key, entry)
                if analysis_result:
                    # Store in new cache
                    success = self.cache.put_analysis(analysis_result)
                    if success:
                        self.migration_stats['migrated'] += 1
                        logger.debug(f"Migrated: {guid_key}")
                    else:
                        self.migration_stats['errors'] += 1
                        logger.warning(f"Failed to store migrated entry: {guid_key}")
                else:
                    self.migration_stats['skipped'] += 1
                    logger.debug(f"Skipped invalid entry: {guid_key}")
                    
            except Exception as e:
                self.migration_stats['errors'] += 1
                logger.error(f"Error migrating entry {guid_key}: {e}")
        
        logger.info(f"Migration completed: {self.migration_stats}")
        return self.migration_stats
    
    def migrate_decision_files(self, decisions_dir: Path) -> Dict[str, int]:
        """
        Migrate legacy decision files from workspace/preflight_decisions.
        
        Args:
            decisions_dir: Path to the preflight_decisions directory
            
        Returns:
            Dictionary with migration statistics
        """
        if not decisions_dir.exists():
            logger.warning(f"Decisions directory not found: {decisions_dir}")
            return self.migration_stats
        
        logger.info(f"Starting migration from decision files in {decisions_dir}")
        
        # Process movie decision files
        movies_dir = decisions_dir / "movies"
        if movies_dir.exists():
            for decision_file in movies_dir.glob("*.json"):
                self._migrate_decision_file(decision_file, "movie")
        
        # Process TV decision files
        tv_dir = decisions_dir / "tv_shows"
        if tv_dir.exists():
            for decision_file in tv_dir.glob("*.json"):
                self._migrate_decision_file(decision_file, "tv")
        
        logger.info(f"Decision files migration completed: {self.migration_stats}")
        return self.migration_stats
    
    def _migrate_decision_file(self, decision_file: Path, content_type: str):
        """Migrate a single decision file."""
        try:
            with open(decision_file, 'r', encoding='utf-8') as f:
                decision_data = json.load(f)
            
            candidates = decision_data.get('candidates', [])
            for candidate in candidates:
                self.migration_stats['processed'] += 1
                
                try:
                    analysis_result = self._convert_decision_candidate(candidate, content_type)
                    if analysis_result:
                        success = self.cache.put_analysis(analysis_result)
                        if success:
                            self.migration_stats['migrated'] += 1
                        else:
                            self.migration_stats['errors'] += 1
                    else:
                        self.migration_stats['skipped'] += 1
                        
                except Exception as e:
                    self.migration_stats['errors'] += 1
                    logger.error(f"Error migrating candidate from {decision_file}: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing decision file {decision_file}: {e}")
    
    def _convert_legacy_entry(self, guid_key: str, entry: Dict[str, Any]) -> Optional[AnalysisResult]:
        """Convert legacy decision history entry to AnalysisResult."""
        try:
            # Parse GUID key format: "Indexer:GUID"
            if ':' not in guid_key:
                logger.warning(f"Invalid GUID key format: {guid_key}")
                return None
            
            indexer, guid = guid_key.split(':', 1)
            
            # Extract basic data
            decision = entry.get('decision', 'UNKNOWN')
            risk_score = entry.get('risk', 0.0)
            missing_ratio = entry.get('missing_ratio', 0.0)
            timestamp = entry.get('ts', time.time())
            
            # We don't have enough metadata from legacy format to create proper content keys
            # So we'll create a minimal entry with what we have
            # This is mainly to preserve the GUID mappings
            
            # Try to extract some info from the GUID if it's a URL
            title = "Unknown"
            if guid.startswith('http'):
                # Extract from URL if possible
                url_parts = guid.split('/')
                if len(url_parts) > 1:
                    title = url_parts[-1]
            
            # Create a basic content key (this won't be very useful for matching)
            content_key = ContentKey(
                content_type='movie',  # Default assumption
                title=title
            )
            
            return AnalysisResult(
                content_key=content_key,
                decision=decision,
                risk_score=risk_score,
                probe_missing_ratio=missing_ratio,
                file_count=0,  # Unknown
                data_segments=0,  # Unknown
                estimated_parity_blocks=0,  # Unknown
                age_days=0,  # Unknown
                poster="",  # Unknown
                groups=[],  # Unknown
                size_bytes=0,  # Unknown
                indexer=indexer,
                title=title,
                guid=guid,
                timestamp=timestamp,
                analysis_version="legacy"
            )
            
        except Exception as e:
            logger.error(f"Error converting legacy entry {guid_key}: {e}")
            return None
    
    def _convert_decision_candidate(self, candidate: Dict[str, Any], content_type: str) -> Optional[AnalysisResult]:
        """Convert decision file candidate to AnalysisResult."""
        try:
            # Extract metadata
            guid = candidate.get('guid', '')
            title = candidate.get('title', 'Unknown')
            indexer = self._extract_indexer_from_guid(guid)
            
            # Create content key based on title and content type
            if content_type == 'movie':
                # Try to extract year from title
                year_match = re.search(r'\b(19|20)(\d{2})\b', title)
                year = int(year_match.group(0)) if year_match else None
                
                content_key = ContentKey(
                    content_type='movie',
                    title=self._extract_clean_title(title, content_type),
                    year=year,
                    quality=extract_quality_from_title(title)
                )
            else:  # TV
                # Extract season/episode info
                season = episode = None
                
                # Try various TV patterns
                tv_match = re.search(r'[Ss](\d{1,2})[Ee](\d{1,2})', title)
                if tv_match:
                    season = int(tv_match.group(1))
                    episode = int(tv_match.group(2))
                
                content_key = ContentKey(
                    content_type='tv',
                    title=self._extract_clean_title(title, content_type),
                    season=season,
                    episode=episode,
                    quality=extract_quality_from_title(title)
                )
            
            return AnalysisResult(
                content_key=content_key,
                decision=candidate.get('decision', 'UNKNOWN'),
                risk_score=candidate.get('risk_score', 0.0),
                probe_missing_ratio=candidate.get('probe_missing_ratio', 0.0),
                file_count=candidate.get('file_count', 0),
                data_segments=candidate.get('data_segments', 0),
                estimated_parity_blocks=candidate.get('estimated_parity_blocks', 0),
                age_days=candidate.get('age_days', 0),
                poster=candidate.get('poster', ''),
                groups=candidate.get('groups', []),
                size_bytes=candidate.get('size', 0),
                indexer=indexer,
                title=title,
                guid=guid,
                timestamp=time.time(),  # Use current time for migration
                analysis_version="migrated",
                sample_size=candidate.get('sample_size'),
                probe_error_ratio=candidate.get('probe_error_ratio'),
                risk_components=candidate.get('components')
            )
            
        except Exception as e:
            logger.error(f"Error converting decision candidate: {e}")
            return None
    
    def _extract_indexer_from_guid(self, guid: str) -> str:
        """Extract indexer name from GUID."""
        if 'nzbfinder' in guid.lower():
            return 'NZBFinder (Prowlarr)'
        elif 'nzbgeek' in guid.lower():
            return 'NZBGeek'
        elif 'dognzb' in guid.lower():
            return 'DogNZB'
        else:
            return 'Unknown'
    
    def _extract_clean_title(self, title: str, content_type: str) -> str:
        """Extract clean title for content key generation."""
        clean_title = title
        
        # Remove quality indicators
        quality_patterns = [
            r'\b\d+p\b', r'\b4k\b', r'\buhd\b', r'\bweb-?dl\b', r'\bbluray\b',
            r'\bhdtv\b', r'\bx264\b', r'\bx265\b', r'\bhevc\b'
        ]
        for pattern in quality_patterns:
            clean_title = re.sub(pattern, '', clean_title, flags=re.IGNORECASE)
        
        if content_type == 'movie':
            # Remove year for movies
            clean_title = re.sub(r'\b(19|20)\d{2}\b', '', clean_title)
        else:  # TV
            # Remove season/episode info for TV
            clean_title = re.sub(r'[Ss]\d{1,2}[Ee]\d{1,2}', '', clean_title)
            clean_title = re.sub(r'\d{1,2}x\d{1,2}', '', clean_title)
        
        # Clean up punctuation and spacing
        clean_title = re.sub(r'[._-]', ' ', clean_title)
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        
        return clean_title
    
    def create_backup(self, backup_path: Path) -> bool:
        """Create backup of current cache state."""
        try:
            stats = self.cache.get_cache_stats()
            backup_data = {
                'timestamp': time.time(),
                'stats': stats,
                'migration_info': self.migration_stats
            }
            
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2)
            
            logger.info(f"Created cache backup at {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            return False
