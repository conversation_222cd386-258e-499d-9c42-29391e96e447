"""
Data models and schema definitions for the multi-layer cache architecture.

This module defines the SQLite schema and Python data structures for:
- L1: In-memory cache (LRU with TTL)
- L2: Persistent SQLite cache 
- GUID mapping and content-based keys
"""

from __future__ import annotations
import hashlib
import re
import sqlite3
import time
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple
from datetime import datetime, timezone


@dataclass
class ContentKey:
    """Content-based cache key that's stable across GUID changes."""
    content_type: str  # 'movie' or 'tv'
    title: str
    year: Optional[int] = None
    season: Optional[int] = None
    episode: Optional[int] = None
    quality: Optional[str] = None
    
    def __post_init__(self):
        """Normalize fields for consistent key generation."""
        self.title = self._normalize_title(self.title)
        if self.quality:
            self.quality = self._normalize_quality(self.quality)
    
    @staticmethod
    def _normalize_title(title: str) -> str:
        """Normalize title for consistent matching."""
        # Remove common punctuation and normalize spacing
        normalized = re.sub(r'[^\w\s]', '', title.lower())
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        return normalized
    
    @staticmethod
    def _normalize_quality(quality: str) -> str:
        """Normalize quality tags for consistent matching."""
        quality = quality.lower()
        # Standardize common quality patterns
        quality = re.sub(r'(\d+)p', r'\1p', quality)  # 1080p, 720p, etc.
        quality = re.sub(r'web.?dl', 'webdl', quality)
        quality = re.sub(r'blu.?ray', 'bluray', quality)
        return quality
    
    def to_string(self) -> str:
        """Generate string representation for cache key."""
        if self.content_type == 'movie':
            key = f"movie:{self.title}"
            if self.year:
                key += f":{self.year}"
            if self.quality:
                key += f":{self.quality}"
        else:  # tv
            key = f"tv:{self.title}"
            if self.season is not None:
                key += f":s{self.season:02d}"
            if self.episode is not None:
                key += f"e{self.episode:02d}"
            if self.quality:
                key += f":{self.quality}"
        
        return key
    
    def to_hash(self) -> str:
        """Generate hash for database storage."""
        return hashlib.sha256(self.to_string().encode()).hexdigest()[:16]

    @classmethod
    def from_string(cls, key_string: str) -> 'ContentKey':
        """Parse content key from string representation."""
        parts = key_string.split(':')
        if len(parts) < 2:
            raise ValueError(f"Invalid content key format: {key_string}")

        content_type = parts[0]
        title = parts[1]

        if content_type == 'movie':
            year = int(parts[2]) if len(parts) > 2 and parts[2].isdigit() else None
            quality = parts[3] if len(parts) > 3 else None
            return cls(content_type='movie', title=title, year=year, quality=quality)

        elif content_type == 'tv':
            season = episode = quality = None
            for part in parts[2:]:
                if part.startswith('s') and 'e' in part:
                    # Format: s01e05
                    season_ep = part[1:]  # Remove 's'
                    if 'e' in season_ep:
                        s_part, e_part = season_ep.split('e', 1)
                        season = int(s_part) if s_part.isdigit() else None
                        episode = int(e_part) if e_part.isdigit() else None
                elif not part.startswith('s') and not 'e' in part:
                    quality = part

            return cls(content_type='tv', title=title, season=season, episode=episode, quality=quality)

        else:
            raise ValueError(f"Unknown content type: {content_type}")


@dataclass
class AnalysisResult:
    """Complete analysis result with metadata."""
    content_key: ContentKey
    decision: str
    risk_score: float
    probe_missing_ratio: float
    file_count: int
    data_segments: int
    estimated_parity_blocks: int
    age_days: int
    poster: str
    groups: List[str]
    size_bytes: int
    indexer: str
    title: str
    guid: str
    timestamp: float
    analysis_version: str = "1.0"
    
    # Additional metadata
    sample_size: Optional[int] = None
    probe_error_ratio: Optional[float] = None
    risk_components: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        result = asdict(self)
        result['content_key'] = self.content_key.to_string()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AnalysisResult':
        """Create from dictionary (for deserialization)."""
        # Parse content key
        content_key_str = data.pop('content_key')
        content_key = ContentKey.from_string(content_key_str)
        
        return cls(content_key=content_key, **data)


@dataclass
class CacheEntry:
    """Internal cache entry with metadata."""
    content_key_hash: str
    content_key_string: str
    analysis_data: bytes  # JSON-serialized AnalysisResult
    created_at: float
    last_accessed: float
    access_count: int
    guids: Set[str]  # All known GUIDs for this content
    
    def touch(self):
        """Update access timestamp and count."""
        self.last_accessed = time.time()
        self.access_count += 1


class CacheSchema:
    """SQLite schema definitions for the persistent cache."""
    
    CREATE_TABLES = [
        """
        CREATE TABLE IF NOT EXISTS analysis_cache (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            content_key_hash TEXT UNIQUE NOT NULL,
            content_key_string TEXT NOT NULL,
            content_type TEXT NOT NULL,
            title TEXT NOT NULL,
            year INTEGER,
            season INTEGER,
            episode INTEGER,
            quality TEXT,
            
            -- Analysis results
            decision TEXT NOT NULL,
            risk_score REAL NOT NULL,
            probe_missing_ratio REAL NOT NULL,
            file_count INTEGER NOT NULL,
            data_segments INTEGER NOT NULL,
            estimated_parity_blocks INTEGER NOT NULL,
            age_days INTEGER NOT NULL,
            poster TEXT,
            groups TEXT,  -- JSON array
            size_bytes INTEGER NOT NULL,
            indexer TEXT NOT NULL,
            original_title TEXT NOT NULL,
            
            -- Metadata
            created_at REAL NOT NULL,
            last_accessed REAL NOT NULL,
            access_count INTEGER DEFAULT 1,
            analysis_version TEXT DEFAULT '1.0',
            
            -- Additional analysis data (JSON)
            extended_data TEXT
        )
        """,
        
        """
        CREATE TABLE IF NOT EXISTS guid_aliases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            guid TEXT UNIQUE NOT NULL,
            content_key_hash TEXT NOT NULL,
            indexer TEXT NOT NULL,
            created_at REAL NOT NULL,
            
            FOREIGN KEY (content_key_hash) REFERENCES analysis_cache (content_key_hash)
        )
        """,
        
        """
        CREATE TABLE IF NOT EXISTS cache_metadata (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL,
            updated_at REAL NOT NULL
        )
        """
    ]
    
    CREATE_INDEXES = [
        "CREATE INDEX IF NOT EXISTS idx_analysis_content_type ON analysis_cache (content_type)",
        "CREATE INDEX IF NOT EXISTS idx_analysis_title ON analysis_cache (title)",
        "CREATE INDEX IF NOT EXISTS idx_analysis_created_at ON analysis_cache (created_at)",
        "CREATE INDEX IF NOT EXISTS idx_analysis_last_accessed ON analysis_cache (last_accessed)",
        "CREATE INDEX IF NOT EXISTS idx_guid_aliases_guid ON guid_aliases (guid)",
        "CREATE INDEX IF NOT EXISTS idx_guid_aliases_content_key ON guid_aliases (content_key_hash)",
        "CREATE INDEX IF NOT EXISTS idx_guid_aliases_indexer ON guid_aliases (indexer)"
    ]
    
    @classmethod
    def initialize_database(cls, db_path: Path) -> sqlite3.Connection:
        """Initialize the cache database with schema."""
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(str(db_path), check_same_thread=False)
        conn.execute("PRAGMA foreign_keys = ON")
        conn.execute("PRAGMA journal_mode = WAL")
        conn.execute("PRAGMA synchronous = NORMAL")
        
        # Create tables
        for table_sql in cls.CREATE_TABLES:
            conn.execute(table_sql)
        
        # Create indexes
        for index_sql in cls.CREATE_INDEXES:
            conn.execute(index_sql)
        
        # Initialize metadata
        conn.execute(
            "INSERT OR IGNORE INTO cache_metadata (key, value, updated_at) VALUES (?, ?, ?)",
            ("schema_version", "1.0", time.time())
        )
        
        conn.commit()
        return conn


# Content key parsing utilities
def parse_movie_content_key(title: str, year: Optional[int] = None, quality: Optional[str] = None) -> ContentKey:
    """Create content key for movie."""
    return ContentKey(
        content_type='movie',
        title=title,
        year=year,
        quality=quality
    )


def parse_tv_content_key(title: str, season: Optional[int] = None, episode: Optional[int] = None, 
                        quality: Optional[str] = None) -> ContentKey:
    """Create content key for TV episode."""
    return ContentKey(
        content_type='tv',
        title=title,
        season=season,
        episode=episode,
        quality=quality
    )


def extract_quality_from_title(title: str) -> Optional[str]:
    """Extract quality information from release title."""
    quality_patterns = [
        r'(\d+p)',  # 1080p, 720p, 480p
        r'(4K|UHD)',  # 4K, UHD
        r'(WEB-?DL|WEBDL)',  # WEB-DL
        r'(BluRay|Blu-Ray|BDREMUX)',  # BluRay variants
        r'(HDTV|SDTV)',  # TV sources
        r'(x264|x265|H\.264|H\.265|HEVC|AV1)',  # Codecs
    ]
    
    qualities = []
    title_lower = title.lower()
    
    for pattern in quality_patterns:
        matches = re.findall(pattern, title_lower, re.IGNORECASE)
        qualities.extend(matches)
    
    return '.'.join(qualities) if qualities else None
