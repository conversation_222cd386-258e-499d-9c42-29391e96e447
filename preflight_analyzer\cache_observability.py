"""
Cache Observability and Metrics.

This module provides comprehensive monitoring, logging, and metrics collection
for the multi-layer cache architecture.
"""

from __future__ import annotations
import json
import logging
import time
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Any, Dict, List, Optional, Deque
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


@dataclass
class CacheEvent:
    """Represents a cache operation event for logging and metrics."""
    timestamp: float
    event_type: str  # 'hit', 'miss', 'put', 'reconciliation', 'error'
    cache_layer: str  # 'L1', 'L2', 'reconciliation'
    guid: str
    content_key: Optional[str] = None
    indexer: Optional[str] = None
    latency_ms: Optional[float] = None
    details: Optional[Dict[str, Any]] = None


class CacheMetrics:
    """Collects and manages cache performance metrics."""
    
    def __init__(self, max_events: int = 10000):
        self.max_events = max_events
        self.events: Deque[CacheEvent] = deque(maxlen=max_events)
        
        # Counters
        self.counters = defaultdict(int)
        
        # Latency tracking
        self.latencies = defaultdict(list)
        
        # Time-based metrics
        self.hourly_stats = defaultdict(lambda: defaultdict(int))
        
        logger.info(f"Initialized cache metrics with max_events={max_events}")
    
    def record_event(self, event: CacheEvent):
        """Record a cache event."""
        self.events.append(event)
        
        # Update counters
        self.counters[f"{event.cache_layer}_{event.event_type}"] += 1
        self.counters[f"total_{event.event_type}"] += 1
        
        # Track latency if provided
        if event.latency_ms is not None:
            self.latencies[f"{event.cache_layer}_{event.event_type}"].append(event.latency_ms)
            # Keep only recent latencies to prevent memory growth
            if len(self.latencies[f"{event.cache_layer}_{event.event_type}"]) > 1000:
                self.latencies[f"{event.cache_layer}_{event.event_type}"] = \
                    self.latencies[f"{event.cache_layer}_{event.event_type}"][-500:]
        
        # Update hourly stats
        hour_key = datetime.fromtimestamp(event.timestamp).strftime('%Y-%m-%d-%H')
        self.hourly_stats[hour_key][f"{event.cache_layer}_{event.event_type}"] += 1
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics."""
        total_requests = (self.counters['total_hit'] + self.counters['total_miss'])
        
        stats = {
            'requests': {
                'total': total_requests,
                'hits': self.counters['total_hit'],
                'misses': self.counters['total_miss'],
                'puts': self.counters['total_put'],
                'hit_rate': self.counters['total_hit'] / total_requests if total_requests > 0 else 0.0
            },
            'by_layer': {
                'L1': {
                    'hits': self.counters['L1_hit'],
                    'misses': self.counters['L1_miss'],
                    'puts': self.counters['L1_put']
                },
                'L2': {
                    'hits': self.counters['L2_hit'],
                    'misses': self.counters['L2_miss'],
                    'puts': self.counters['L2_put']
                },
                'reconciliation': {
                    'successes': self.counters['reconciliation_hit'],
                    'failures': self.counters['reconciliation_miss']
                }
            },
            'errors': self.counters['total_error'],
            'total_events': len(self.events)
        }
        
        # Add latency statistics
        stats['latencies'] = {}
        for key, latencies in self.latencies.items():
            if latencies:
                stats['latencies'][key] = {
                    'count': len(latencies),
                    'avg_ms': sum(latencies) / len(latencies),
                    'min_ms': min(latencies),
                    'max_ms': max(latencies),
                    'p95_ms': self._percentile(latencies, 95),
                    'p99_ms': self._percentile(latencies, 99)
                }
        
        return stats
    
    def get_recent_events(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent cache events."""
        recent = list(self.events)[-limit:]
        return [asdict(event) for event in recent]
    
    def get_hourly_breakdown(self, hours: int = 24) -> Dict[str, Dict[str, int]]:
        """Get hourly breakdown of cache activity."""
        # Get the last N hours
        current_time = time.time()
        result = {}
        
        for i in range(hours):
            hour_timestamp = current_time - (i * 3600)
            hour_key = datetime.fromtimestamp(hour_timestamp).strftime('%Y-%m-%d-%H')
            result[hour_key] = dict(self.hourly_stats.get(hour_key, {}))
        
        return result
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of a list of numbers."""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = int((percentile / 100.0) * len(sorted_data))
        if index >= len(sorted_data):
            index = len(sorted_data) - 1
        
        return sorted_data[index]
    
    def export_metrics(self, output_path: Path) -> bool:
        """Export metrics to JSON file."""
        try:
            metrics_data = {
                'timestamp': time.time(),
                'summary': self.get_summary_stats(),
                'recent_events': self.get_recent_events(500),
                'hourly_breakdown': self.get_hourly_breakdown(48)
            }
            
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, indent=2)
            
            logger.info(f"Exported cache metrics to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export metrics: {e}")
            return False


class CacheLogger:
    """Enhanced logging for cache operations."""
    
    def __init__(self, metrics: CacheMetrics, log_level: int = logging.INFO):
        self.metrics = metrics
        self.logger = logging.getLogger('cache_operations')
        self.logger.setLevel(log_level)
        
        # Create formatter for cache-specific logs
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Add console handler if not already present
        if not self.logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
    
    def log_cache_hit(self, cache_layer: str, guid: str, content_key: str, 
                     latency_ms: float, indexer: str = None):
        """Log a cache hit event."""
        event = CacheEvent(
            timestamp=time.time(),
            event_type='hit',
            cache_layer=cache_layer,
            guid=guid,
            content_key=content_key,
            indexer=indexer,
            latency_ms=latency_ms
        )
        
        self.metrics.record_event(event)
        self.logger.info(f"💾 {cache_layer} cache hit: {content_key} (GUID: {guid[:8]}..., {latency_ms:.1f}ms)")
    
    def log_cache_miss(self, cache_layer: str, guid: str, latency_ms: float, indexer: str = None):
        """Log a cache miss event."""
        event = CacheEvent(
            timestamp=time.time(),
            event_type='miss',
            cache_layer=cache_layer,
            guid=guid,
            indexer=indexer,
            latency_ms=latency_ms
        )
        
        self.metrics.record_event(event)
        self.logger.debug(f"❌ {cache_layer} cache miss: GUID {guid[:8]}... ({latency_ms:.1f}ms)")
    
    def log_cache_put(self, cache_layer: str, guid: str, content_key: str, 
                     latency_ms: float, indexer: str = None):
        """Log a cache put operation."""
        event = CacheEvent(
            timestamp=time.time(),
            event_type='put',
            cache_layer=cache_layer,
            guid=guid,
            content_key=content_key,
            indexer=indexer,
            latency_ms=latency_ms
        )
        
        self.metrics.record_event(event)
        self.logger.debug(f"💾 {cache_layer} cache put: {content_key} (GUID: {guid[:8]}..., {latency_ms:.1f}ms)")
    
    def log_guid_reconciliation(self, guid: str, matched_content_key: str, 
                              confidence: float, reasons: List[str], 
                              latency_ms: float, indexer: str = None):
        """Log a successful GUID reconciliation."""
        event = CacheEvent(
            timestamp=time.time(),
            event_type='hit',  # Reconciliation is effectively a hit
            cache_layer='reconciliation',
            guid=guid,
            content_key=matched_content_key,
            indexer=indexer,
            latency_ms=latency_ms,
            details={
                'confidence': confidence,
                'reasons': reasons
            }
        )
        
        self.metrics.record_event(event)
        self.logger.info(f"🔗 GUID reconciliation: {guid[:8]}... -> {matched_content_key} "
                        f"(confidence: {confidence:.3f}, reasons: {', '.join(reasons)}, {latency_ms:.1f}ms)")
    
    def log_reconciliation_failure(self, guid: str, latency_ms: float, indexer: str = None):
        """Log a failed GUID reconciliation attempt."""
        event = CacheEvent(
            timestamp=time.time(),
            event_type='miss',
            cache_layer='reconciliation',
            guid=guid,
            indexer=indexer,
            latency_ms=latency_ms
        )
        
        self.metrics.record_event(event)
        self.logger.debug(f"❌ GUID reconciliation failed: {guid[:8]}... ({latency_ms:.1f}ms)")
    
    def log_cache_error(self, cache_layer: str, guid: str, error: str, 
                       latency_ms: float = None, indexer: str = None):
        """Log a cache error."""
        event = CacheEvent(
            timestamp=time.time(),
            event_type='error',
            cache_layer=cache_layer,
            guid=guid,
            indexer=indexer,
            latency_ms=latency_ms,
            details={'error': error}
        )
        
        self.metrics.record_event(event)
        self.logger.error(f"🚨 {cache_layer} cache error: {error} (GUID: {guid[:8]}...)")
    
    def log_cache_stats_summary(self):
        """Log a summary of cache statistics."""
        stats = self.metrics.get_summary_stats()
        req_stats = stats['requests']
        
        self.logger.info(
            f"📊 Cache Stats Summary: "
            f"hit_rate={req_stats['hit_rate']:.1%} "
            f"(hits: {req_stats['hits']}, misses: {req_stats['misses']}), "
            f"L1_hits: {stats['by_layer']['L1']['hits']}, "
            f"L2_hits: {stats['by_layer']['L2']['hits']}, "
            f"reconciliations: {stats['by_layer']['reconciliation']['successes']}, "
            f"errors: {stats['errors']}"
        )


class CacheHealthMonitor:
    """Monitors cache health and performance."""
    
    def __init__(self, metrics: CacheMetrics, 
                 hit_rate_threshold: float = 0.8,
                 error_rate_threshold: float = 0.05):
        self.metrics = metrics
        self.hit_rate_threshold = hit_rate_threshold
        self.error_rate_threshold = error_rate_threshold
        
        self.alerts = []
    
    def check_health(self) -> Dict[str, Any]:
        """Check cache health and return status."""
        stats = self.metrics.get_summary_stats()
        health_status = {
            'overall_status': 'healthy',
            'checks': {},
            'alerts': []
        }
        
        # Check hit rate
        hit_rate = stats['requests']['hit_rate']
        if hit_rate < self.hit_rate_threshold:
            health_status['overall_status'] = 'warning'
            health_status['alerts'].append(f"Low hit rate: {hit_rate:.1%} < {self.hit_rate_threshold:.1%}")
        
        health_status['checks']['hit_rate'] = {
            'status': 'ok' if hit_rate >= self.hit_rate_threshold else 'warning',
            'value': hit_rate,
            'threshold': self.hit_rate_threshold
        }
        
        # Check error rate
        total_requests = stats['requests']['total']
        error_rate = stats['errors'] / total_requests if total_requests > 0 else 0.0
        
        if error_rate > self.error_rate_threshold:
            health_status['overall_status'] = 'critical'
            health_status['alerts'].append(f"High error rate: {error_rate:.1%} > {self.error_rate_threshold:.1%}")
        
        health_status['checks']['error_rate'] = {
            'status': 'ok' if error_rate <= self.error_rate_threshold else 'critical',
            'value': error_rate,
            'threshold': self.error_rate_threshold
        }
        
        # Check for recent activity
        recent_events = len([e for e in self.metrics.events if time.time() - e.timestamp < 3600])
        health_status['checks']['recent_activity'] = {
            'status': 'ok' if recent_events > 0 else 'warning',
            'value': recent_events,
            'description': 'Events in last hour'
        }
        
        return health_status
    
    def get_performance_report(self) -> str:
        """Generate a human-readable performance report."""
        stats = self.metrics.get_summary_stats()
        health = self.check_health()
        
        report = [
            "=== Cache Performance Report ===",
            f"Overall Status: {health['overall_status'].upper()}",
            "",
            "Request Statistics:",
            f"  Total Requests: {stats['requests']['total']:,}",
            f"  Hit Rate: {stats['requests']['hit_rate']:.1%}",
            f"  L1 Hits: {stats['by_layer']['L1']['hits']:,}",
            f"  L2 Hits: {stats['by_layer']['L2']['hits']:,}",
            f"  Reconciliations: {stats['by_layer']['reconciliation']['successes']:,}",
            f"  Errors: {stats['errors']:,}",
            ""
        ]
        
        # Add latency information if available
        if stats.get('latencies'):
            report.append("Latency Statistics:")
            for operation, latency_stats in stats['latencies'].items():
                report.append(f"  {operation}:")
                report.append(f"    Average: {latency_stats['avg_ms']:.1f}ms")
                report.append(f"    P95: {latency_stats['p95_ms']:.1f}ms")
                report.append(f"    P99: {latency_stats['p99_ms']:.1f}ms")
            report.append("")
        
        # Add alerts if any
        if health['alerts']:
            report.append("Alerts:")
            for alert in health['alerts']:
                report.append(f"  ⚠️  {alert}")
        
        return "\n".join(report)
