"""
Legacy DecisionHistory interface - now powered by the enhanced multi-layer cache.

This module maintains backward compatibility while providing the new
robust cache architecture underneath.
"""

from __future__ import annotations
import logging
from pathlib import Path
from typing import Any, Dict, Optional

# Import the enhanced cache system
from .enhanced_cache import EnhancedDecisionCache

logger = logging.getLogger(__name__)


class DecisionHistory:
    """
    Legacy DecisionHistory interface using the new multi-layer cache.

    This class maintains the same API as the original DecisionHistory
    but uses the enhanced cache system underneath for better performance
    and GUID reconciliation capabilities.
    """

    def __init__(self, path: Path, ttl_seconds: int = 12 * 3600):
        """
        Initialize decision history with enhanced caching.

        Args:
            path: Path to legacy cache file (used for migration)
            ttl_seconds: TTL for memory cache layer
        """
        self.path = path
        self.ttl = ttl_seconds

        # Use the enhanced cache system
        self._enhanced_cache = EnhancedDecisionCache(path, ttl_seconds)

        logger.info(f"Initialized DecisionHistory with enhanced cache (legacy path: {path})")

    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get cached decision by key.

        Args:
            key: Cache key in format "Indexer:GUID"

        Returns:
            Dictionary with decision data or None if not found/expired
        """
        return self._enhanced_cache.get(key)

    def put(self, key: str, decision: str, report: Dict[str, Any]) -> None:
        """
        Store decision in cache.

        Args:
            key: Cache key in format "Indexer:GUID"
            decision: Decision result ('ACCEPT', 'REJECT_INCOMPLETE', etc.)
            report: Full analysis report
        """
        self._enhanced_cache.put(key, decision, report)

    # Additional methods for enhanced functionality
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return self._enhanced_cache.get_stats()

    def get_health_status(self) -> Dict[str, Any]:
        """Get cache health status."""
        return self._enhanced_cache.get_health_status()

    def get_performance_report(self) -> str:
        """Get human-readable performance report."""
        return self._enhanced_cache.get_performance_report()

    def cleanup_expired(self) -> int:
        """Clean up expired cache entries."""
        return self._enhanced_cache.cleanup_expired()

    def close(self):
        """Close cache and cleanup resources."""
        self._enhanced_cache.close()
