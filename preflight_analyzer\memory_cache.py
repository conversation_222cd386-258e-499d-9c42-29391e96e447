"""
L1 In-Memory Cache Layer - Fast LRU cache for hot analysis results.

This module implements the in-memory cache layer of the multi-layer cache architecture.
It provides extremely fast access to recently used analysis results with TTL expiration.
"""

from __future__ import annotations
import logging
import threading
import time
from typing import Any, Dict, Optional, Tuple

try:
    from cachetools import TTLCache
except ImportError:
    # Fallback implementation if cachetools is not available
    from collections import OrderedDict
    import threading
    
    class TTLCache:
        """Simple TTL cache implementation as fallback."""
        
        def __init__(self, maxsize: int, ttl: float):
            self.maxsize = maxsize
            self.ttl = ttl
            self._data = OrderedDict()
            self._lock = threading.RLock()
        
        def __getitem__(self, key):
            with self._lock:
                if key not in self._data:
                    raise KeyError(key)
                
                value, timestamp = self._data[key]
                if time.time() - timestamp > self.ttl:
                    del self._data[key]
                    raise KeyError(key)
                
                # Move to end (LRU)
                self._data.move_to_end(key)
                return value
        
        def __setitem__(self, key, value):
            with self._lock:
                # Remove expired entries
                self._expire_old()
                
                # Add new entry
                self._data[key] = (value, time.time())
                self._data.move_to_end(key)
                
                # Enforce size limit
                while len(self._data) > self.maxsize:
                    self._data.popitem(last=False)
        
        def __contains__(self, key):
            try:
                self[key]
                return True
            except KeyError:
                return False
        
        def get(self, key, default=None):
            try:
                return self[key]
            except KeyError:
                return default
        
        def pop(self, key, default=None):
            with self._lock:
                if key in self._data:
                    value, _ = self._data.pop(key)
                    return value
                return default
        
        def clear(self):
            with self._lock:
                self._data.clear()
        
        def _expire_old(self):
            """Remove expired entries."""
            current_time = time.time()
            expired_keys = []
            
            for key, (value, timestamp) in self._data.items():
                if current_time - timestamp > self.ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._data[key]
        
        def __len__(self):
            with self._lock:
                self._expire_old()
                return len(self._data)

from .cache_models import AnalysisResult, ContentKey

logger = logging.getLogger(__name__)


class MemoryCache:
    """L1 in-memory cache for fast access to recent analysis results."""
    
    def __init__(self, maxsize: int = 1000, ttl_seconds: int = 3600):
        """
        Initialize memory cache.
        
        Args:
            maxsize: Maximum number of entries to keep in memory
            ttl_seconds: Time-to-live for cache entries in seconds
        """
        self.maxsize = maxsize
        self.ttl_seconds = ttl_seconds
        
        # Separate caches for different lookup patterns
        self._guid_cache = TTLCache(maxsize=maxsize, ttl=ttl_seconds)
        self._content_key_cache = TTLCache(maxsize=maxsize, ttl=ttl_seconds)
        
        # Statistics
        self._stats = {
            'hits': 0,
            'misses': 0,
            'puts': 0,
            'evictions': 0
        }
        self._stats_lock = threading.Lock()
        
        logger.info(f"Initialized memory cache with maxsize={maxsize}, ttl={ttl_seconds}s")
    
    def get_by_guid(self, guid: str) -> Optional[AnalysisResult]:
        """Get analysis result by GUID."""
        try:
            result = self._guid_cache[guid]
            self._record_hit()
            logger.debug(f"Memory cache hit for GUID: {guid}")
            return result
        except KeyError:
            self._record_miss()
            return None
    
    def get_by_content_key(self, content_key: ContentKey) -> Optional[AnalysisResult]:
        """Get analysis result by content key."""
        cache_key = content_key.to_string()
        try:
            result = self._content_key_cache[cache_key]
            self._record_hit()
            logger.debug(f"Memory cache hit for content key: {cache_key}")
            return result
        except KeyError:
            self._record_miss()
            return None
    
    def put(self, analysis_result: AnalysisResult) -> None:
        """Store analysis result in memory cache."""
        content_key_str = analysis_result.content_key.to_string()
        
        # Store in both caches for different lookup patterns
        self._content_key_cache[content_key_str] = analysis_result
        self._guid_cache[analysis_result.guid] = analysis_result
        
        self._record_put()
        logger.debug(f"Stored in memory cache: {content_key_str} (GUID: {analysis_result.guid})")
    
    def put_with_guid_alias(self, guid: str, analysis_result: AnalysisResult) -> None:
        """Store analysis result with additional GUID alias."""
        # Store the main entry
        self.put(analysis_result)
        
        # Add the alias GUID mapping
        if guid != analysis_result.guid:
            self._guid_cache[guid] = analysis_result
            logger.debug(f"Added GUID alias in memory cache: {guid} -> {analysis_result.content_key.to_string()}")
    
    def invalidate_guid(self, guid: str) -> bool:
        """Remove entry by GUID."""
        try:
            result = self._guid_cache.pop(guid)
            if result:
                # Also remove from content key cache
                content_key_str = result.content_key.to_string()
                self._content_key_cache.pop(content_key_str, None)
                logger.debug(f"Invalidated memory cache entry for GUID: {guid}")
                return True
        except KeyError:
            pass
        return False
    
    def invalidate_content_key(self, content_key: ContentKey) -> bool:
        """Remove entry by content key."""
        content_key_str = content_key.to_string()
        try:
            result = self._content_key_cache.pop(content_key_str)
            if result:
                # Also remove from GUID cache
                self._guid_cache.pop(result.guid, None)
                logger.debug(f"Invalidated memory cache entry for content key: {content_key_str}")
                return True
        except KeyError:
            pass
        return False
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self._guid_cache.clear()
        self._content_key_cache.clear()
        logger.info("Cleared memory cache")
    
    def _record_hit(self):
        """Record cache hit for statistics."""
        with self._stats_lock:
            self._stats['hits'] += 1
    
    def _record_miss(self):
        """Record cache miss for statistics."""
        with self._stats_lock:
            self._stats['misses'] += 1
    
    def _record_put(self):
        """Record cache put for statistics."""
        with self._stats_lock:
            self._stats['puts'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._stats_lock:
            stats = self._stats.copy()
        
        # Add current cache sizes
        stats['guid_cache_size'] = len(self._guid_cache)
        stats['content_key_cache_size'] = len(self._content_key_cache)
        stats['maxsize'] = self.maxsize
        stats['ttl_seconds'] = self.ttl_seconds
        
        # Calculate hit rate
        total_requests = stats['hits'] + stats['misses']
        stats['hit_rate'] = stats['hits'] / total_requests if total_requests > 0 else 0.0
        
        return stats
    
    def get_cache_info(self) -> str:
        """Get human-readable cache information."""
        stats = self.get_stats()
        return (
            f"Memory Cache Stats: "
            f"hits={stats['hits']}, misses={stats['misses']}, "
            f"hit_rate={stats['hit_rate']:.1%}, "
            f"size={stats['guid_cache_size']}/{stats['maxsize']}, "
            f"ttl={stats['ttl_seconds']}s"
        )
    
    def warm_up(self, analysis_results: list[AnalysisResult]) -> int:
        """Pre-populate cache with analysis results."""
        count = 0
        for result in analysis_results:
            self.put(result)
            count += 1
        
        logger.info(f"Warmed up memory cache with {count} entries")
        return count
    
    def expire_old_entries(self) -> int:
        """Manually expire old entries (useful for testing)."""
        # For TTLCache, this happens automatically on access
        # But we can force it by accessing a non-existent key
        try:
            _ = self._guid_cache['__force_expire__']
        except KeyError:
            pass
        
        try:
            _ = self._content_key_cache['__force_expire__']
        except KeyError:
            pass
        
        return 0  # TTLCache handles expiration internally
